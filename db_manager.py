
import logging

import logging
import os
import json
import requests
from datetime import datetime, timezone
import pytz
from sqlalchemy import text
from sqlalchemy.exc import SQLAlchemyError
from google.cloud.sql.connector import Connector

# Import timezone utilities
from utils import utc_now, ist_now, to_utc, to_ist

# Configure logging
logger = logging.getLogger(__name__)

def update_campaign_status(pool, campaign_id, template_id, email_status, processed_rows=0, emails_sent=0, errors=None):
    """
    Update the status of an Email campaign.

    Args:
        pool: Database connection pool
        campaign_id: ID of the campaign
        template_id: ID of the template
        email_status: Status of the campaign (sending, completed, failed)
        processed_rows: Number of rows processed
        emails_sent: Number of emails sent successfully
        errors: List of error messages (optional)
    """
    # First, ensure we're only updating with valid values
    if processed_rows < 0 or emails_sent < 0:
        logger.error(f"Invalid values for update: processed_rows={processed_rows}, emails_sent={emails_sent}")
        return False
    
    # Process errors field
    if errors is None:
        errors = []
    
    # Convert errors to list if it's a single string
    if isinstance(errors, str):
        errors = [errors]
    
    # Convert to JSON string for database
    errors_json = json.dumps(errors)
        
    # Log detailed information for debugging
    logger.info(f"Status: {email_status}, Processed: {processed_rows}, Sent: {emails_sent}, Errors: {len(errors)}")

    # Use UTC timestamp for database updates
    current_utc = utc_now()

    update_query = text("""
        UPDATE notice_emailtemplate
        SET email_status = :email_status, 
            email_processed_rows = :processed_rows, 
            email_messages_sent = :emails_sent,
            email_errors = :errors,
            updated_at = :updated_at
        WHERE id = :template_id
        RETURNING id, email_status, email_processed_rows, email_messages_sent;
    """)

    try:
        with pool.connect() as connection:
            params = {
                "email_status": email_status,
                "processed_rows": processed_rows,
                "emails_sent": emails_sent,
                "errors": errors_json,
                "template_id": template_id,
                "updated_at": current_utc  # Store UTC in database
            }
            
            # Execute query and capture return values to verify update
            result = connection.execute(update_query, params)
            row = result.fetchone()
            connection.commit()
            
            if row:
                return True
            else:
                logger.error(f"Update failed - No row found for template_id={template_id}")
                return False
                
    except Exception as e:
        logger.error(f"Error updating email campaign status in database: {str(e)}")
        logger.error(f"Parameters: campaign_id={campaign_id}, template_id={template_id}, "
                    f"status={email_status}, processed={processed_rows}, sent={emails_sent}")
        return False

# def create_email_entry(pool, loan_id, msg_id, email, status, timestamp, user_id=None, failed_reason=None, is_co_borrower=False, borrower_type='primary'):
#     """
#     Create an entry in the email_entry table.

#     Args:
#         pool: Database connection pool
#         loan_id: Loan ID associated with the email
#         msg_id: Email message ID
#         email: Recipient's email address
#         status: Message status (sent, delivered, read, failed)
#         timestamp: Message timestamp (should be in UTC)
#         user_id: User ID (optional)
#         failed_reason: Reason for failure (optional)
#         is_co_borrower: Whether the recipient is a co-borrower (optional)
#         borrower_type: Type of borrower (primary, co-borrower-1, etc.)
#     """
#     try:
#         # Ensure timestamp is in UTC
#         if timestamp is not None:
#             timestamp = to_utc(timestamp)
#         else:
#             timestamp = utc_now()

#         with pool.connect() as connection:
#             email_entry_query = text("""
#                 INSERT INTO notice_emailentry (msg_id, email, status, timestamp, user_id, loan_id, failed_reason, is_co_borrower, borrower_type)
#                 VALUES (:msg_id, :email, :status, :timestamp, :user_id, :loan_id, :failed_reason, :is_co_borrower, :borrower_type)
#                 RETURNING id
#             """)

#             params = {
#                 "msg_id": msg_id,
#                 "email": email,
#                 "status": status,
#                 "timestamp": timestamp,
#                 "user_id": user_id,
#                 "loan_id": loan_id,
#                 "failed_reason": failed_reason,
#                 "is_co_borrower": is_co_borrower,
#                 "borrower_type": borrower_type
#             }

#             result = connection.execute(email_entry_query, params)
#             entry_id = result.fetchone()[0]  # Get the returned ID
#             connection.commit()
#             return entry_id

#     except Exception as e:
#         logger.error(f"Error creating email entry: {str(e)}")
#         return None

# TODO: uncomment this once new db design is created.(today-21/07/2025)
# def create_email_entries_batch(pool, entries):
#     """
#     Create multiple email entries in the email_entry table in a single transaction.

#     Args:
#         pool: Database connection pool
#         entries: List of dictionaries containing email entry data
        
#     Returns:
#         List of created entry IDs or None on error
#     """
#     if not entries:
#         return []

#     try:
#         entry_ids = []

#         with pool.connect() as connection:
#             # Start transaction
#             transaction = connection.begin()

#             for entry in entries:
#                 # Ensure timestamp is in UTC
#                 timestamp = entry.get('timestamp')
#                 if timestamp is not None:
#                     timestamp = to_utc(timestamp)
#                 else:
#                     timestamp = utc_now()

#                 # Get borrower type with default of 'primary'
#                 borrower_type = entry.get('borrower_type', 'primary')

#                 email_entry_query = text("""
#                     INSERT INTO notice_emailentry (msg_id, email, status, timestamp, user_id, loan_id, failed_reason, is_co_borrower, borrower_type)
#                     VALUES (:msg_id, :email, :status, :timestamp, :user_id, :loan_id, :failed_reason, :is_co_borrower, :borrower_type)
#                     RETURNING id
#                 """)

#                 params = {
#                     "msg_id": entry.get('msg_id'),
#                     "email": entry.get('email'),
#                     "status": entry.get('status', 'sent'),
#                     "timestamp": timestamp,
#                     "user_id": entry.get('user_id'),
#                     "loan_id": entry.get('loan_id'),
#                     "failed_reason": entry.get('failed_reason'),
#                     "is_co_borrower": entry.get('is_co_borrower', False),
#                     "borrower_type": borrower_type
#                 }

#                 result = connection.execute(email_entry_query, params)
#                 entry_id = result.fetchone()[0]
#                 entry_ids.append(entry_id)

#             # Commit transaction
#             transaction.commit()
            
#             # Log summary with count of primary and co-borrowers
#             primary_count = sum(1 for e in entries if e.get('borrower_type', 'primary') == 'primary')
#             co_borrower_count = len(entries) - primary_count
            
#             return entry_ids

#     except SQLAlchemyError as e:
#         logger.error(f"Error creating batch email entries: {str(e)}")
#         # Transaction will be automatically rolled back
#         return None

def get_dispute_ids_by_loan_ids(pool, loan_ids, campaign_id):
    """Get dispute IDs for multiple loan_ids in a single query"""
    # Convert loan_ids to a comma-separated string with quotes
    if not loan_ids:
        return {}
        
    # Create a unique list of loan IDs (to handle cases where primary and co-borrowers share the same loan ID)
    unique_loan_ids = list(set([str(loan_id) for loan_id in loan_ids if loan_id]))
    
    if not unique_loan_ids:
        return {}
        
    loan_ids_str = "', '".join(unique_loan_ids)
    loan_ids_str = f"('{loan_ids_str}')"

    query = text(f"""
        SELECT id, loan_id 
        FROM odr_dispute 
        WHERE loan_id IN {loan_ids_str} AND campaign_id = :campaign_id
    """)

    try:
        with pool.connect() as connection:
            result = connection.execute(query, {"campaign_id": campaign_id})
            # Create a mapping of loan_id to dispute_id
            loan_to_dispute_map = {row[1]: row[0] for row in result}
            return loan_to_dispute_map
    except Exception as e:
        logger.error(f"Error fetching dispute IDs: {str(e)}")
        return {}
        
def get_co_borrower_counts(pool, campaign_id):
    """
    Get count of co-borrowers by loan ID for a specific campaign.
    
    Args:
        pool: Database connection pool
        campaign_id: ID of the campaign
        
    Returns:
        Dictionary mapping loan IDs to co-borrower counts
    """
    query = text("""
        SELECT loan_id, COUNT(*) as co_borrower_count
        FROM notice_emailentry
        WHERE campaign_id = :campaign_id AND (is_co_borrower = TRUE OR borrower_type LIKE 'co-borrower%')
        GROUP BY loan_id
    """)
    
    try:
        with pool.connect() as connection:
            result = connection.execute(query, {"campaign_id": campaign_id})
            # Create a mapping of loan_id to co-borrower count
            loan_to_count_map = {row[0]: row[1] for row in result}
            return loan_to_count_map
    except Exception as e:
        logger.error(f"Error fetching co-borrower counts: {str(e)}")
        return {}
        
def update_campaign_co_borrower_stats(pool, campaign_id, template_id):
    """
    Update campaign statistics with co-borrower information.
    
    Args:
        pool: Database connection pool
        campaign_id: ID of the campaign
        template_id: ID of the template
    """
    try:
        # First, get total co-borrower emails sent
        co_borrower_count_query = text("""
            SELECT COUNT(*) 
            FROM notice_emailentry 
            WHERE campaign_id = :campaign_id AND (is_co_borrower = TRUE OR borrower_type LIKE 'co-borrower%') AND status = 'sent'
        """)
        
        # Get primary borrower emails sent
        primary_count_query = text("""
            SELECT COUNT(*) 
            FROM notice_emailentry 
            WHERE campaign_id = :campaign_id AND borrower_type = 'primary' AND status = 'sent'
        """)
        
        # Use UTC timestamp for database updates - THIS IS THE KEY FIX!
        current_utc = utc_now()
        
        # Then update the template with co-borrower statistics
        update_query = text("""
            UPDATE notice_emailtemplate  
            SET co_borrower_emails_sent = :co_borrower_count,
            primary_emails_sent = :primary_count,
            updated_at = :updated_at  -- ✅ This uses explicit UTC timestamp
            WHERE id = :template_id
        """)
        
        with pool.connect() as connection:
            # Get co-borrower count
            co_result = connection.execute(co_borrower_count_query, {"campaign_id": campaign_id})
            co_borrower_count = co_result.fetchone()[0]
            
            # Get primary borrower count
            primary_result = connection.execute(primary_count_query, {"campaign_id": campaign_id})
            primary_count = primary_result.fetchone()[0]
            
            # Update template
            connection.execute(update_query, {
                "co_borrower_count": co_borrower_count,
                "primary_count": primary_count,
                "template_id": template_id,
                "updated_at": current_utc  # ✅ FIXED: Store UTC in database instead of NOW()
            })
            connection.commit()
            
    except Exception as e:
        logger.error(f"Error updating co-borrower stats: {str(e)}")

def get_borrower_type_statistics(pool, campaign_id):
    """
    Get detailed statistics about email sending by borrower type.
    
    Args:
        pool: Database connection pool
        campaign_id: ID of the campaign
        
    Returns:
        Dictionary with statistics breakdown
    """
    query = text("""
        SELECT 
            borrower_type,
            status,
            COUNT(*) as count
        FROM notice_emailentry
        WHERE campaign_id = :campaign_id
        GROUP BY borrower_type, status
        ORDER BY borrower_type, status
    """)
    
    try:
        with pool.connect() as connection:
            result = connection.execute(query, {"campaign_id": campaign_id})
            
            stats = {
                'primary': {'total': 0, 'successful': 0, 'failed': 0},
                'co_borrower': {'total': 0, 'successful': 0, 'failed': 0}
            }
            
            for row in result:
                borrower_type, status, count = row
                
                # Categorize borrower type
                if borrower_type == 'primary':
                    category = 'primary'
                elif borrower_type.startswith('co-borrower') or borrower_type == 'co_borrower':
                    category = 'co_borrower'
                else:
                    continue
                
                stats[category]['total'] += count
                
                # Categorize status
                if status in ['sent', 'delivered', 'read']:
                    stats[category]['successful'] += count
                elif status in ['failed']:
                    stats[category]['failed'] += count
            
            return stats
    except Exception as e:
        logger.error(f"Error fetching borrower type statistics: {str(e)}")
        return {
            'primary': {'total': 0, 'successful': 0, 'failed': 0},
            'co_borrower': {'total': 0, 'successful': 0, 'failed': 0}
        }



def update_master_excel_status(pool, campaign_id, status=False):
    """
    Update the master_excel_generated status for a campaign.
    Args:
        pool: Database connection pool
        campaign_id: ID of the campaign to update
        status: Boolean status to set (default: False)
    Returns:
        bool: True if update successful, False otherwise
    """
    if not campaign_id:
        logger.error("Campaign ID is required for updating master_excel_generated status")
        return False

    update_query = text("""
        UPDATE notice_campaign
        SET master_excel_generated = :status,
            updated_at = NOW()
        WHERE id = :campaign_id
        RETURNING id, master_excel_generated;
    """)

    try:
        with pool.connect() as connection:
            result = connection.execute(update_query, {
                "status": status,
                "campaign_id": campaign_id
            })
            row = result.fetchone()
            connection.commit()

            if row:
                logger.info(f"Successfully updated master_excel_generated to {status} for campaign {campaign_id}")
                return True
            else:
                logger.error(f"No campaign found with ID {campaign_id}")
                return False

    except Exception as e:
        logger.error(f"Error updating master_excel_generated status for campaign {campaign_id}: {str(e)}")
        return False
